#!/usr/bin/env python
"""
Test script to verify HTML download functionality works correctly.
This simulates the career page HTML generation and download process.
"""

import os
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'canvider.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.http import JsonResponse
from feed.models import Employee, Employer
from feed.views import generate_careers_page_HTML, generate_widget_html, generate_full_page_html

def test_html_download_endpoint():
    """Test the HTML download endpoint with various configurations"""
    print("=" * 60)
    print("TESTING HTML DOWNLOAD ENDPOINT")
    print("=" * 60)
    
    # Create a test client
    client = Client()
    
    # Test data configurations
    test_configs = [
        {
            'name': 'Basic Configuration',
            'data': {
                'company_name': 'Test Company',
                'tagline': 'Join our amazing team',
                'logo_url': 'https://example.com/logo.png',
                'banner_url': 'https://example.com/banner.png',
                'rss_feed_url': 'https://workloupe.com/company/test/jobs.rss',
                'second_title': 'Open Positions',
                'second_tagline': 'Explore opportunities',
                'primary_color': '#007bff',
                'secondary_color': '#6c757d',
                'enable_search': True
            }
        },
        {
            'name': 'Minimal Configuration',
            'data': {
                'company_name': 'Minimal Co',
                'tagline': '',
                'logo_url': '',
                'banner_url': '',
                'rss_feed_url': '',
                'second_title': '',
                'second_tagline': '',
                'primary_color': '',
                'secondary_color': '',
                'enable_search': False
            }
        },
        {
            'name': 'Special Characters Configuration',
            'data': {
                'company_name': 'Test & Company <script>alert("xss")</script>',
                'tagline': 'Join our "amazing" team & grow!',
                'logo_url': 'https://example.com/logo with spaces.png',
                'banner_url': 'https://example.com/banner&test.png',
                'rss_feed_url': 'https://workloupe.com/company/test/jobs.rss',
                'second_title': 'Open <Positions>',
                'second_tagline': 'Explore & discover opportunities',
                'primary_color': '#007bff',
                'secondary_color': '#6c757d',
                'enable_search': True
            }
        }
    ]
    
    for i, config in enumerate(test_configs, 1):
        print(f"\nTest {i}: {config['name']}")
        print("-" * 40)
        
        try:
            # Test direct function call (simulating the view logic)
            html_content = generate_full_page_html(
                company_name=config['data']['company_name'],
                tagline=config['data']['tagline'],
                logo_url=config['data']['logo_url'],
                banner_url=config['data']['banner_url'],
                rss_feed_url=config['data']['rss_feed_url'],
                second_title=config['data']['second_title'],
                second_tagline=config['data']['second_tagline'],
                primary_color=config['data']['primary_color'],
                secondary_color=config['data']['secondary_color'],
                enable_search=config['data']['enable_search'],
                is_full_page=True,
                is_widget=False
            )
            
            if html_content and len(html_content) > 500:
                print("✅ HTML generation successful")
                print(f"   Content length: {len(html_content)} characters")
                
                # Check for proper HTML structure
                checks = [
                    ('DOCTYPE', '<!DOCTYPE html>' in html_content),
                    ('HTML tags', '<html' in html_content and '</html>' in html_content),
                    ('Head section', '<head>' in html_content and '</head>' in html_content),
                    ('Body section', '<body>' in html_content and '</body>' in html_content),
                    ('Title tag', '<title>' in html_content),
                    ('Bootstrap CSS', 'bootstrap' in html_content),
                    ('Font Awesome', 'font-awesome' in html_content),
                ]
                
                for check_name, check_result in checks:
                    status = "✅" if check_result else "❌"
                    print(f"   {status} {check_name}")
                
                # Check for XSS protection
                if '<script>alert(' not in html_content:
                    print("   ✅ XSS protection working")
                else:
                    print("   ❌ XSS vulnerability detected")
                
                # Check for proper escaping
                if '&lt;script&gt;' in html_content or 'Test &amp; Company' in html_content:
                    print("   ✅ HTML escaping working")
                else:
                    print("   ⚠️  HTML escaping may not be working (or no special chars to escape)")
                
            else:
                print("❌ HTML generation failed or content too short")
                print(f"   Content: {html_content[:200]}...")
                
        except Exception as e:
            print(f"❌ Error generating HTML: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)

def test_widget_html_generation():
    """Test widget HTML generation specifically"""
    print("TESTING WIDGET HTML GENERATION")
    print("=" * 60)
    
    try:
        widget_html = generate_widget_html(
            company_name="Test Company",
            tagline="Join our team",
            logo_url="https://example.com/logo.png",
            banner_url="https://example.com/banner.png",
            rss_feed_url="https://workloupe.com/company/test/jobs.rss",
            second_title="Open Positions",
            second_tagline="Explore opportunities",
            primary_color="#007bff",
            secondary_color="#6c757d",
            max_jobs=5,
            show_salary=True,
            show_location=True,
            show_date=True
        )
        
        if widget_html and len(widget_html) > 100:
            print("✅ Widget HTML generated successfully")
            print(f"   Content length: {len(widget_html)} characters")
            
            # Check for key components
            checks = [
                ('RSS feed container', 'rssFeedContainer' in widget_html),
                ('Job card structure', 'job-card' in widget_html),
                ('CSS styling', '<style>' in widget_html),
                ('JavaScript functionality', '<script>' in widget_html),
                ('Company name', 'Test Company' in widget_html),
            ]
            
            for check_name, check_result in checks:
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}")
                
        else:
            print("❌ Widget HTML generation failed")
            print(f"   Content: {widget_html}")
            
    except Exception as e:
        print(f"❌ Error generating widget HTML: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)

def test_json_response_format():
    """Test that the view returns proper JSON responses"""
    print("TESTING JSON RESPONSE FORMAT")
    print("=" * 60)
    
    # Simulate the view's JSON handling
    test_data = {
        'company_name': 'Test Company',
        'tagline': 'Join our team',
        'logo_url': 'https://example.com/logo.png',
        'banner_url': 'https://example.com/banner.png',
        'rss_feed_url': 'https://workloupe.com/company/test/jobs.rss',
        'second_title': 'Open Positions',
        'second_tagline': 'Explore opportunities',
        'primary_color': '#007bff',
        'secondary_color': '#6c757d',
        'enable_search': True
    }
    
    try:
        # Test JSON serialization
        json_data = json.dumps(test_data)
        parsed_data = json.loads(json_data)
        
        print("✅ JSON serialization/deserialization working")
        
        # Test that all required fields are present
        required_fields = ['company_name', 'tagline', 'rss_feed_url']
        missing_fields = [field for field in required_fields if not parsed_data.get(field)]
        
        if not missing_fields:
            print("✅ All required fields present")
        else:
            print(f"❌ Missing required fields: {missing_fields}")
        
        # Test HTML generation with parsed data
        html_content = generate_full_page_html(
            company_name=parsed_data['company_name'],
            tagline=parsed_data['tagline'],
            logo_url=parsed_data['logo_url'],
            banner_url=parsed_data['banner_url'],
            rss_feed_url=parsed_data['rss_feed_url'],
            second_title=parsed_data['second_title'],
            second_tagline=parsed_data['second_tagline'],
            primary_color=parsed_data['primary_color'],
            secondary_color=parsed_data['secondary_color'],
            enable_search=parsed_data['enable_search'],
            is_full_page=True,
            is_widget=False
        )
        
        if html_content and len(html_content) > 500:
            print("✅ HTML generation from JSON data successful")
            
            # Create a mock JsonResponse
            response_data = {
                'success': True,
                'html_content': html_content,
                'filename': f"{parsed_data['company_name']}_careers_page.html"
            }
            
            print("✅ JSON response format valid")
            print(f"   Response keys: {list(response_data.keys())}")
            
        else:
            print("❌ HTML generation from JSON data failed")
            
    except Exception as e:
        print(f"❌ Error in JSON processing: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)

def main():
    """Run all HTML download tests"""
    print("RUNNING HTML DOWNLOAD FUNCTIONALITY TESTS")
    print("=" * 60)
    print("Testing the career page HTML download functionality:")
    print("1. HTML generation with various configurations")
    print("2. Widget HTML generation")
    print("3. JSON response format handling")
    print("=" * 60)
    
    try:
        test_html_download_endpoint()
        test_widget_html_generation()
        test_json_response_format()
        
        print("ALL HTML DOWNLOAD TESTS COMPLETED")
        print("=" * 60)
        print("✅ If all tests show PASSED, the HTML download functionality is working!")
        print("❌ If any tests show FAILED, there may be issues to investigate.")
        
    except Exception as e:
        print(f"❌ Error running HTML download tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
