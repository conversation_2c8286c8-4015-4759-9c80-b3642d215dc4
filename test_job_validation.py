#!/usr/bin/env python
"""
Test script to verify job description validation functionality works correctly.
This simulates the job publishing process with various job descriptions.
"""

import os
import django
import json
from bs4 import BeautifulSoup
import re

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'canvider.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.http import JsonResponse
from feed.models import Employee, Employer, Vacancy

def simulate_job_description_validation(job_description):
    """Simulate the job description validation logic from save_published_job view"""
    try:
        if not job_description:
            return False, 'Job description is required.'
        
        # Extract text content from HTML and count actual characters
        soup = BeautifulSoup(job_description, 'html.parser')
        text_content = soup.get_text()
        
        # Remove extra whitespace and count characters
        clean_text = re.sub(r'\s+', ' ', text_content).strip()
        text_length = len(clean_text)
        
        if text_length < 40:
            return False, f'Job description must contain at least 40 characters of actual text content. Current length: {text_length} characters.'
        
        return True, f'Valid job description with {text_length} characters.'
        
    except Exception as e:
        # Fallback validation - check if description is too short
        if len(job_description.strip()) < 40:
            return False, 'Job description must contain at least 40 characters of content.'
        return True, 'Valid job description (fallback validation).'

def test_job_description_validation():
    """Test job description validation with comprehensive test cases"""
    print("=" * 60)
    print("TESTING JOB DESCRIPTION VALIDATION")
    print("=" * 60)
    
    test_cases = [
        {
            'name': 'Empty description',
            'description': '',
            'expected_valid': False,
            'expected_error_contains': 'required'
        },
        {
            'name': 'Very short text',
            'description': '<p>Short</p>',
            'expected_valid': False,
            'expected_error_contains': '40 characters'
        },
        {
            'name': 'Only HTML tags with whitespace',
            'description': '<p>   </p><div>   </div><br><hr>',
            'expected_valid': False,
            'expected_error_contains': '40 characters'
        },
        {
            'name': 'Exactly 40 characters',
            'description': '<p>This job description has exactly forty chars.</p>',
            'expected_valid': True,
            'expected_error_contains': None
        },
        {
            'name': 'Just over 40 characters',
            'description': '<p>This job description has exactly forty characters and a bit more.</p>',
            'expected_valid': True,
            'expected_error_contains': None
        },
        {
            'name': 'Rich HTML with sufficient content',
            'description': '''
            <h2>Software Engineer Position</h2>
            <p>We are looking for a talented <strong>Software Engineer</strong> to join our team.</p>
            <ul>
                <li>Develop web applications</li>
                <li>Work with modern technologies</li>
                <li>Collaborate with team members</li>
            </ul>
            <p>Requirements: 3+ years experience in Python/Django.</p>
            ''',
            'expected_valid': True,
            'expected_error_contains': None
        },
        {
            'name': 'HTML with insufficient text content',
            'description': '''
            <div>
                <h1></h1>
                <p>   </p>
                <ul>
                    <li></li>
                    <li></li>
                </ul>
                <p>Short text</p>
            </div>
            ''',
            'expected_valid': False,
            'expected_error_contains': '40 characters'
        },
        {
            'name': 'Mixed content with line breaks',
            'description': '''
            Job Title: Senior Developer
            
            We are seeking an experienced developer to join our growing team.
            
            Responsibilities include:
            - Code development
            - Testing
            - Documentation
            ''',
            'expected_valid': True,
            'expected_error_contains': None
        },
        {
            'name': 'XSS attempt with valid content',
            'description': '''
            <script>alert('xss')</script>
            <p>This is a legitimate job description for a software engineer position. 
            We need someone with experience in web development and security practices.</p>
            ''',
            'expected_valid': True,
            'expected_error_contains': None
        },
        {
            'name': 'Only special characters and symbols',
            'description': '<p>!@#$%^&*()_+-=[]{}|;:,.<>?</p>',
            'expected_valid': False,
            'expected_error_contains': '40 characters'
        }
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print("-" * 40)
        print(f"Input: {test_case['description'][:100]}{'...' if len(test_case['description']) > 100 else ''}")
        
        is_valid, message = simulate_job_description_validation(test_case['description'])
        
        print(f"Validation result: {'VALID' if is_valid else 'INVALID'}")
        print(f"Message: {message}")
        print(f"Expected: {'VALID' if test_case['expected_valid'] else 'INVALID'}")
        
        # Check if the result matches expectations
        result_correct = is_valid == test_case['expected_valid']
        
        # Check if error message contains expected text (for invalid cases)
        message_correct = True
        if not test_case['expected_valid'] and test_case['expected_error_contains']:
            message_correct = test_case['expected_error_contains'].lower() in message.lower()
            if not message_correct:
                print(f"Expected error message to contain: '{test_case['expected_error_contains']}'")
        
        if result_correct and message_correct:
            print("✅ Test PASSED")
            passed_tests += 1
        else:
            print("❌ Test FAILED")
            if not result_correct:
                print(f"   Expected {'VALID' if test_case['expected_valid'] else 'INVALID'}, got {'VALID' if is_valid else 'INVALID'}")
            if not message_correct:
                print(f"   Error message doesn't contain expected text")
    
    print("\n" + "=" * 60)
    print(f"VALIDATION TEST SUMMARY: {passed_tests}/{total_tests} tests passed")
    print("=" * 60)
    
    return passed_tests == total_tests

def test_edge_cases():
    """Test edge cases and potential security issues"""
    print("TESTING EDGE CASES AND SECURITY")
    print("=" * 60)
    
    edge_cases = [
        {
            'name': 'Very large description',
            'description': '<p>' + 'A' * 10000 + '</p>',
            'expected_valid': True
        },
        {
            'name': 'Unicode characters',
            'description': '<p>Développeur logiciel recherché pour équipe internationale. Expérience requise en développement web moderne. 🚀</p>',
            'expected_valid': True
        },
        {
            'name': 'Nested HTML structures',
            'description': '''
            <div>
                <section>
                    <article>
                        <header>
                            <h1>Job Title</h1>
                        </header>
                        <main>
                            <p>We are looking for a qualified candidate with strong technical skills and experience.</p>
                        </main>
                    </article>
                </section>
            </div>
            ''',
            'expected_valid': True
        },
        {
            'name': 'Malformed HTML',
            'description': '<p>This is a job description with <strong>unclosed tags and <em>nested elements that might cause parsing issues but should still work.</p>',
            'expected_valid': True
        },
        {
            'name': 'Only numbers and punctuation',
            'description': '<p>123456789012345678901234567890123456789012345678901234567890</p>',
            'expected_valid': True
        }
    ]
    
    passed_edge_tests = 0
    total_edge_tests = len(edge_cases)
    
    for i, test_case in enumerate(edge_cases, 1):
        print(f"\nEdge Test {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            is_valid, message = simulate_job_description_validation(test_case['description'])
            
            print(f"Validation result: {'VALID' if is_valid else 'INVALID'}")
            print(f"Message: {message}")
            
            if is_valid == test_case['expected_valid']:
                print("✅ Edge test PASSED")
                passed_edge_tests += 1
            else:
                print("❌ Edge test FAILED")
                
        except Exception as e:
            print(f"❌ Edge test FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"EDGE CASE TEST SUMMARY: {passed_edge_tests}/{total_edge_tests} tests passed")
    print("=" * 60)
    
    return passed_edge_tests == total_edge_tests

def test_performance():
    """Test performance with various description sizes"""
    print("TESTING PERFORMANCE")
    print("=" * 60)
    
    import time
    
    performance_tests = [
        {'size': 100, 'name': 'Small description'},
        {'size': 1000, 'name': 'Medium description'},
        {'size': 10000, 'name': 'Large description'},
        {'size': 100000, 'name': 'Very large description'}
    ]
    
    for test in performance_tests:
        description = '<p>' + 'A' * test['size'] + '</p>'
        
        start_time = time.time()
        is_valid, message = simulate_job_description_validation(description)
        end_time = time.time()
        
        duration = (end_time - start_time) * 1000  # Convert to milliseconds
        
        print(f"{test['name']} ({test['size']} chars): {duration:.2f}ms - {'VALID' if is_valid else 'INVALID'}")
        
        if duration > 1000:  # More than 1 second
            print("⚠️  Performance warning: validation took more than 1 second")
        else:
            print("✅ Performance acceptable")
    
    print("\n" + "=" * 60)

def main():
    """Run all job description validation tests"""
    print("RUNNING JOB DESCRIPTION VALIDATION TESTS")
    print("=" * 60)
    print("Testing the job description validation functionality:")
    print("1. Basic validation with various inputs")
    print("2. Edge cases and security considerations")
    print("3. Performance testing")
    print("=" * 60)
    
    try:
        basic_tests_passed = test_job_description_validation()
        edge_tests_passed = test_edge_cases()
        test_performance()
        
        print("ALL JOB DESCRIPTION VALIDATION TESTS COMPLETED")
        print("=" * 60)
        
        if basic_tests_passed and edge_tests_passed:
            print("✅ ALL TESTS PASSED - Job description validation is working correctly!")
        else:
            print("❌ SOME TESTS FAILED - There may be issues to investigate.")
            if not basic_tests_passed:
                print("   - Basic validation tests failed")
            if not edge_tests_passed:
                print("   - Edge case tests failed")
        
    except Exception as e:
        print(f"❌ Error running job description validation tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
