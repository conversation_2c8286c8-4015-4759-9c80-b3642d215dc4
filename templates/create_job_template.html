{% extends 'main.html' %}

{% load static %}
{% load i18n %}
{% block content %}
<!-- Include Material Icons -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<!-- Include Font Awesome for the editor icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<div class="container">
    <div class="templates-header">
        <h1>{% trans "Templates" %}</h1>
        <p class="templates-subtitle">{% trans "Create and manage reusable job description templates" %}</p>

        <nav class="breadcrumb">
            <a href="{% url 'settings' %}">{% trans "Settings" %}</a>
            <span class="material-icons">chevron_right</span>
            <span>{% trans "Templates" %}</span>
        </nav>
    </div>

    <!-- Templates Stats -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">description</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">{{ templates|length }}</div>
                <div class="stat-label">{% trans "Total Templates" %}</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">calendar_today</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">{{ templates_this_month }}</div>
                <div class="stat-label">{% trans "Created This Month" %}</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">trending_up</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">{{ total_usage }}</div>
                <div class="stat-label">{% trans "Jobs Created from Templates" %}</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">access_time</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">{{ time_saved_percent }}%</div>
                <div class="stat-label">{% trans "Time Saved Using Templates" %}</div>
            </div>
        </div>
    </div>

    <!-- Main Templates Layout -->
    <div class="templates-layout">
        <!-- Templates List -->
        <div class="templates-sidebar">
            <div class="sidebar-header">
                <h2>{% trans "My Templates" %}</h2>
                <button class="new-template-btn" id="new-template-btn">
                    <span class="material-icons">add</span>
                    {% trans "New Template" %}
                </button>
            </div>

            <div class="search-box">
                <span class="material-icons">search</span>
                <input type="text" id="search-templates" placeholder="{% trans 'Search templates...' %}">
            </div>

            <div class="templates-list" id="templates-list">
                <!-- Template items will be dynamically loaded using JavaScript -->
                <!-- This is just for initial rendering, will be replaced by JS -->
                {% if templates %}
                    {% for template in templates %}
                        <div class="template-item {% if forloop.first %}selected{% endif %}" data-id="{{ template.title }}">
                            <div class="template-title" title="{{ template.title }}">{{ template.title }}</div>
                            <div class="template-meta">
                                <span class="template-date">{% trans "Updated" %} {{ template.updated_at|date:"M d, Y" }}</span>
                                <span class="template-usage">
                                    {% if template.usage_count == 1 %}
                                        {% trans "Used 1 time" %}
                                    {% else %}
                                        {% trans "Used" %} {{ template.usage_count }} {% trans "times" %}
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <!-- Will be replaced by the createNewTemplate function -->
                    <div class="template-item selected" data-id="default">
                        <div class="template-title">{% trans "New Template" %}</div>
                        <div class="template-meta">
                            <span class="template-date">{% trans "Not saved yet" %}</span>
                            <span class="template-usage">{% trans "Not used yet" %}</span>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Template Editor -->
        <div class="template-editor-container">
            <div class="editor-header">
                <input type="text" id="template-title-input" value="{% if templates.0 %}{{ templates.0.title }}{% else %}{% trans 'New Template' %}{% endif %}" placeholder="{% trans 'Enter template title' %}">

                <div class="editor-actions">
                    <button class="action-btn" id="delete-template-btn" title="{% trans 'Delete Template' %}">
                        <span class="material-icons">delete</span>
                    </button>
                    <button class="save-template-btn" id="save-template-btn">
                        <span class="material-icons">save</span>
                        {% trans "Save Template" %}
                    </button>
                </div>
            </div>

            <!-- Rich Text Editor -->
            <div class="editor-wrapper">
                <!-- Rich Text Editor Toolbar -->
                <div class="editor-toolbar">
                    <div class="toolbar-group">
                        <button type="button" data-command="formatBlock" data-value="H1" title="{% trans 'Heading 1' %}">H1</button>
                        <button type="button" data-command="formatBlock" data-value="H2" title="{% trans 'Heading 2' %}">H2</button>
                        <button type="button" data-command="formatBlock" data-value="H3" title="{% trans 'Heading 3' %}">H3</button>
                        <button type="button" data-command="formatBlock" data-value="P" title="{% trans 'Paragraph' %}">P</button>
                    </div>

                    <div class="toolbar-group">
                        <button type="button" data-command="bold" title="{% trans 'Bold' %}"><i class="fas fa-bold"></i></button>
                        <button type="button" data-command="italic" title="{% trans 'Italic' %}"><i class="fas fa-italic"></i></button>
                        <button type="button" data-command="underline" title="{% trans 'Underline' %}"><i class="fas fa-underline"></i></button>
                    </div>

                    <div class="toolbar-group">
                        <button type="button" id="btn-bullet-list" title="{% trans 'Bullet List' %}"><i class="fas fa-list-ul"></i></button>
                        <button type="button" id="btn-number-list" title="{% trans 'Numbered List' %}"><i class="fas fa-list-ol"></i></button>
                    </div>

                    <div class="toolbar-group emoji-group">
                        <button type="button" class="emoji-button" title="Add Emoji">😊</button>
                        <div id="emoji-picker" class="emoji-picker">
                            <div class="emoji-row">
                                <button class="emoji" data-emoji="😊">😊</button>
                                <button class="emoji" data-emoji="😎">😎</button>
                                <button class="emoji" data-emoji="👍">👍</button>
                                <button class="emoji" data-emoji="💼">💼</button>
                                <button class="emoji" data-emoji="🎯">🎯</button>
                            </div>
                            <div class="emoji-row">
                                <button class="emoji" data-emoji="⭐">⭐</button>
                                <button class="emoji" data-emoji="🚀">🚀</button>
                                <button class="emoji" data-emoji="💡">💡</button>
                                <button class="emoji" data-emoji="🔥">🔥</button>
                                <button class="emoji" data-emoji="✨">✨</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rich Text Editor Content Area -->
                <div class="editor-content" id="editor" contenteditable="true">
                    {% if templates.0 %}
                        {{ templates.0.description|safe }}
                    {% else %}
                        <p>{% trans "Enter your template content here..." %}</p>
                    {% endif %}
                </div>

                <!-- Character Counter -->
                <div class="character-counter">
                    <span id="char-count">0</span> / 10000 {% trans "characters" %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="delete-template-modal">
    <div class="modal-content modal-sm">
        <div class="modal-header">
            <h2>{% trans "Delete Template" %}</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <p class="delete-confirmation-message">
                {% trans "Are you sure you want to delete the" %} "<span id="template-to-delete">Software Engineer</span>" {% trans "template? This action cannot be undone." %}
            </p>

            <div class="modal-footer">
                <button type="button" class="cancel-btn" id="cancel-delete">{% trans "Cancel" %}</button>
                <button type="button" class="danger-btn" id="confirm-delete">{% trans "Delete" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notification -->
<div class="toast" id="toast">
    <div class="toast-icon">
        <span class="material-icons success-icon">check_circle</span>
        <span class="material-icons error-icon">error</span>
    </div>
    <div class="toast-message"></div>
    <div class="toast-close">&times;</div>
</div>

<style>
    :root {
        --primary: #4a6cf7;
        --primary-hover: #3859e9;
        --secondary: #f5f8ff;
        --success: #28a745;
        --danger: #dc3545;
        --light-gray: #f1f2f6;
        --text-color: #333;
        --border-color: #ddd;
        --hover-bg: #f9fafb;
        --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
        background-color: #f9fafc;
        color: var(--text-color);
        line-height: 1.6;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    /* Templates Header */
    .templates-header {
        margin-bottom: 30px;
    }

    .templates-header h1 {
        font-size: 28px;
        color: #252b42;
        margin-bottom: 10px;
    }

    .templates-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 20px;
    }

    /* Breadcrumb */
    .breadcrumb {
        display: flex;
        align-items: center;
        font-size: 14px;
    }

    .breadcrumb a {
        color: var(--primary);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .breadcrumb .material-icons {
        font-size: 16px;
        margin: 0 8px;
        color: #999;
    }

    /* Stats Container */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: var(--shadow);
        display: flex;
        align-items: center;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--secondary);
        color: var(--primary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
    }

    .stat-icon .material-icons {
        font-size: 24px;
    }

    .stat-content {
        flex: 1;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #252b42;
    }

    .stat-label {
        font-size: 14px;
        color: #666;
    }

    /* Main Templates Layout */
    .templates-layout {
        display: grid;
        grid-template-columns: 300px 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    @media (max-width: 768px) {
        .templates-layout {
            grid-template-columns: 1fr;
        }
    }

    /* Templates Sidebar */
    .templates-sidebar {
        background-color: white;
        border-radius: 8px;
        box-shadow: var(--shadow);
        display: flex;
        flex-direction: column;
        height: 600px;
    }

    .sidebar-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .sidebar-header h2 {
        font-size: 18px;
        margin: 0;
    }

    .new-template-btn {
        display: flex;
        align-items: center;
        background-color: var(--primary);
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .new-template-btn:hover {
        background-color: var(--primary-hover);
    }

    .new-template-btn .material-icons {
        font-size: 18px;
        margin-right: 5px;
    }

    .search-box {
        position: relative;
        padding: 15px 20px;
        border-bottom: 1px solid var(--border-color);
    }

    .search-box .material-icons {
        position: absolute;
        left: 30px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        font-size: 18px;
    }

    .search-box input {
        width: 100%;
        padding: 8px 10px 8px 35px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
    }

    .search-box input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .templates-list {
        flex: 1;
        overflow-y: auto;
        padding: 10px 0;
    }

    .template-item {
        padding: 15px 20px;
        border-bottom: 1px solid var(--border-color);
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .template-item:last-child {
        border-bottom: none;
    }

    .template-item:hover {
        background-color: var(--hover-bg);
    }

    .template-item.selected {
        background-color: var(--secondary);
        border-left: 4px solid var(--primary);
    }

    .template-title {
        font-weight: 600;
        margin-bottom: 5px;
        white-space: nowrap; /* Keep text on a single line */
        overflow: hidden; /* Hide overflow text */
        text-overflow: ellipsis; /* Show ellipsis for overflow */
        max-width: 100%; /* Ensure it stays within container */
        position: relative; /* For the tooltip positioning */
    }
    .template-title:hover::after {
        content: attr(title); /* Use the title attribute for tooltip content */
        position: absolute;
        left: 0;
        top: 100%;
        background-color: #2d2d2d;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        z-index: 10;
        max-width: 250px;
        word-wrap: break-word;
        white-space: normal;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .template-meta {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #888;
    }

    /* Template Editor */
    .template-editor-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: var(--shadow);
        display: flex;
        flex-direction: column;
        min-height: 600px;
        overflow: hidden;
    }

    .editor-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    #template-title-input {
        font-size: 18px;
        font-weight: 600;
        border: 1px solid #ddd; /* Always show a border */
        border-radius: 4px;
        padding: 8px 12px;
        width: 300px;
        background-color: white; /* Always white background */
    }

    #template-title-input:focus {
        outline: none;
        border-color: var(--primary); /* Highlight with primary color when focused */
        box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2); /* Add a subtle glow */
    }

    .editor-actions {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .action-btn {
        background: none;
        border: none;
        width: 36px;
        height: 36px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        cursor: pointer;
        transition: all 0.3s;
    }

    .action-btn:hover {
        background-color: var(--hover-bg);
        color: var(--danger);
    }

    .save-template-btn {
        background-color: var(--primary);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 5px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .save-template-btn:hover {
        background-color: var(--primary-hover);
    }

    .save-template-btn .material-icons {
        font-size: 18px;
    }

    .editor-wrapper {
        flex: 1;
        padding: 20px;
        display: flex;
        flex-direction: column;
    }

    /* Rich Text Editor */
    .editor-toolbar {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding: 12px;
        border: 1px solid #e0e0e0;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
    }

    .toolbar-group {
        display: flex;
        gap: 4px;
        border-right: 1px solid #e0e0e0;
        padding-right: 8px;
        margin-right: 8px;
    }

    .toolbar-group:last-child {
        border-right: none;
        margin-right: 0;
        padding-right: 0;
    }

    .editor-toolbar button {
        background: none;
        border: 1px solid transparent;
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 3px;
        font-size: 13px;
        color: inherit;
    }

    .editor-toolbar button:hover {
        border-color: #ccc;
    }

    .editor-toolbar button.active {
        border-color: #999;
    }

    .editor-content {
        min-height: 300px;
        max-height: 400px;
        border: 1px solid #e0e0e0;
        border-top: none;
        padding: 20px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        outline: none;
        overflow-y: auto;
        line-height: 1.6;
    }

    /* Ensure proper list display with nesting */
    .editor-content ul {
        list-style-type: disc;
        margin-left: 20px;
    }

    .editor-content ol {
        list-style-type: decimal;
        margin-left: 20px;
    }

    /* Nested list styling */
    .editor-content ul ul {
        list-style-type: circle;
        margin-left: 20px;
    }

    .editor-content ul ul ul {
        list-style-type: square;
        margin-left: 20px;
    }

    .editor-content ol ol {
        list-style-type: lower-alpha;
        margin-left: 20px;
    }

    .editor-content ol ol ol {
        list-style-type: lower-roman;
        margin-left: 20px;
    }

    /* Custom CSS for indented lists */
    .editor-content .custom-indent {
        margin-left: 20px;
    }

    .editor-content:focus {
        border-color: #666666;
        box-shadow: 0 0 0 1px #666666;
    }

    /* Simple Emoji Picker */
    .emoji-group {
        position: relative;
    }

    .emoji-picker {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 8px;
        z-index: 100;
        width: 200px;
    }

    .emoji-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 6px;
    }

    .emoji-row:last-child {
        margin-bottom: 0;
    }

    .emoji {
        cursor: pointer;
        font-size: 16px;
        padding: 4px;
        border-radius: 3px;
        border: 1px solid transparent;
        background: transparent;
    }

    .emoji:hover {
        border-color: #e0e0e0;
    }

    /* Character Counter */
    .character-counter {
        margin-top: 10px;
        text-align: right;
        font-size: 14px;
        color: #666;
    }

    .character-counter.limit-exceeded {
        color: #dc3545;
        font-weight: bold;
    }



    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        overflow: auto;
    }

    .modal-content {
        background-color: white;
        margin: 10% auto;
        max-width: 500px;
        width: 90%;
        border-radius: 8px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        animation: modalFadeIn 0.3s;
    }

    .modal-content.modal-sm {
        max-width: 400px;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
    }

    .close-modal {
        font-size: 24px;
        cursor: pointer;
        color: #999;
    }

    .close-modal:hover {
        color: #333;
    }

    .modal-body {
        padding: 20px;
    }

    .delete-confirmation-message {
        margin-bottom: 20px;
        color: #555;
    }

    #template-to-delete {
        font-weight: 600;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }

    .cancel-btn, .danger-btn {
        padding: 10px 16px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
    }

    .cancel-btn {
        background-color: white;
        color: #666;
        border: 1px solid var(--border-color);
    }

    .cancel-btn:hover {
        background-color: #f5f5f5;
    }

    .danger-btn {
        background-color: var(--danger);
        color: white;
        border: none;
    }

    .danger-btn:hover {
        background-color: #c82333;
    }

    /* Toast Notification */
    .toast {
        position: fixed;
        bottom: 30px;
        right: 30px;
        background-color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        padding: 16px;
        display: flex;
        align-items: center;
        min-width: 300px;
        max-width: 400px;
        z-index: 1000;
        transform: translateY(150%);
        transition: transform 0.3s ease-out;
    }

    .toast.show {
        transform: translateY(0);
    }

    .toast-icon {
        margin-right: 12px;
    }

    .success-icon {
        color: var(--success);
        font-size: 24px;
    }

    .error-icon {
        color: var(--danger);
        font-size: 24px;
        display: none;
    }

    .toast.error .success-icon {
        display: none;
    }

    .toast.error .error-icon {
        display: block;
    }

    .toast-message {
        flex: 1;
        font-size: 14px;
    }

    .toast-close {
        cursor: pointer;
        color: #999;
        font-size: 18px;
        padding-left: 10px;
    }

    .toast-close:hover {
        color: #666;
    }
</style>

<script>

// Define global utility functions first
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Toast notification function - defined early so it's available to all other functions
function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    const toastMessage = document.querySelector('.toast-message');

    toastMessage.textContent = message;

    // Reset classes
    toast.className = 'toast';

    // Add the appropriate classes
    toast.classList.add('show');
    if (type === 'error') {
        toast.classList.add('error');
    }

    // Auto-hide after 5 seconds
    setTimeout(function() {
        toast.classList.remove('show');
    }, 5000);
}

// Character counter update function - defined early
function updateCharCount() {
    const editor = document.getElementById('editor');
    const charCount = document.getElementById('char-count');
    const charCounter = document.querySelector('.character-counter');

    const text = editor.textContent || '';
    const count = text.length;
    charCount.textContent = count;

    // Add visual indicator if exceeding limit
    if (count > 10000) {
        charCounter.classList.add('limit-exceeded');
    } else {
        charCounter.classList.remove('limit-exceeded');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the rich text editor
    initRichTextEditor();

    // Initialize template functions
    loadTemplatesFromDatabase();

    // Setup character counter
    setupCharacterCounter();

    // Initialize emoji picker
    initEmojiPicker();

    // Setup save template functionality
    setupSaveTemplate();

    // Setup delete template functionality
    setupDeleteTemplate();

    // Initialize toast notifications
    initToast();

    // Initialize search functionality
    initSearch();

    // Setup new template button - explicitly bind the event
    document.getElementById('new-template-btn').addEventListener('click', function() {
        createNewTemplate();
    });
});

// Initialize Rich Text Editor
function initRichTextEditor() {
    const buttons = document.querySelectorAll('.editor-toolbar button[data-command]');
    const editor = document.getElementById('editor');

    buttons.forEach(button => {
        button.addEventListener('click', function() {
            const command = this.dataset.command;
            const value = this.dataset.value || '';

            document.execCommand(command, false, value);

            // Toggle active state for formatting buttons
            if (['bold', 'italic', 'underline'].includes(command)) {
                this.classList.toggle('active');
            }

            editor.focus();
            updateCharCount();
        });
    });

    // Fixed list buttons
    document.getElementById('btn-bullet-list').addEventListener('click', function() {
        document.execCommand('insertUnorderedList', false, null);
        editor.focus();
    });

    document.getElementById('btn-number-list').addEventListener('click', function() {
        document.execCommand('insertOrderedList', false, null);
        editor.focus();
    });


}

// Load Templates From Database
function loadTemplatesFromDatabase() {


    fetch('/api/templates/')
        .then(response => response.json())
        .then(data => {
            const templatesList = document.getElementById('templates-list');
            templatesList.innerHTML = ''; // Clear existing templates

            if (data.templates && data.templates.length > 0) {
                data.templates.forEach((template, index) => {
                    // Create a new template item
                    const templateItem = document.createElement('div');
                    templateItem.className = 'template-item';
                    if (index === 0) templateItem.classList.add('selected');
                    templateItem.setAttribute('data-id', template.title);

                    // Format the template metadata
                    const usageText = template.usage_count === 1
                        ? "Used 1 time"
                        : `Used ${template.usage_count} times`;

                    templateItem.innerHTML = `
                        <div class="template-title">${template.title}</div>
                        <div class="template-meta">
                            <span class="template-date">Updated ${template.updated_at}</span>
                            <span class="template-usage">${usageText}</span>
                        </div>
                    `;

                    // Add click event listener
                    templateItem.addEventListener('click', function() {
                        document.querySelectorAll('.template-item').forEach(item => {
                            item.classList.remove('selected');
                        });
                        this.classList.add('selected');

                        // Load the template content
                        loadTemplateContent(template.title);
                    });

                    templatesList.appendChild(templateItem);
                });

                // Load the first template by default
                loadTemplateContent(data.templates[0].title);
            } else {
                // No templates found, create a default one
                createNewTemplate();
            }
        })
        .catch(error => {
            console.error('Error loading templates:', error);
            showToast('Failed to load templates from the server', 'error');
        });
}

// Load Template Content
function loadTemplateContent(title) {
    fetch('/api/get-template/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({ title: title })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Set the title input
            document.getElementById('template-title-input').value = data.title;

            // Set the description in the editor
            document.getElementById('editor').innerHTML = data.description;

            // Update template name in delete modal
            document.getElementById('template-to-delete').textContent = data.title;

            // Update character count
            updateCharCount();
        } else {
            showToast('Failed to load template content', 'error');
        }
    })
    .catch(error => {
        console.error('Error loading template content:', error);
        showToast('Failed to load template content from the server', 'error');
    });
}

// Initialize Emoji Picker
function initEmojiPicker() {
    const editor = document.getElementById('editor');
    const emojiButton = document.querySelector('.emoji-button');
    const emojiPicker = document.getElementById('emoji-picker');

    if (!emojiButton || !emojiPicker) return;

    emojiButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        emojiPicker.style.display = emojiPicker.style.display === 'block' ? 'none' : 'block';
    });

    // Close emoji picker when clicking outside
    document.addEventListener('click', function(e) {
        if (!emojiButton.contains(e.target) && !emojiPicker.contains(e.target)) {
            emojiPicker.style.display = 'none';
        }
    });

    // Add emojis to the editor
    document.querySelectorAll('.emoji').forEach(function(emoji) {
        emoji.addEventListener('click', function(e) {
            e.preventDefault();
            const emojiChar = this.getAttribute('data-emoji');

            // Get current selection
            const selection = window.getSelection();
            const range = selection.getRangeAt(0);

            // Insert emoji at cursor position
            const textNode = document.createTextNode(emojiChar);
            range.deleteContents();
            range.insertNode(textNode);

            // Move cursor after the inserted emoji
            range.setStartAfter(textNode);
            range.setEndAfter(textNode);
            selection.removeAllRanges();
            selection.addRange(range);

            // Hide emoji picker and focus editor
            emojiPicker.style.display = 'none';
            editor.focus();

            // Update character count
            updateCharCount();
        });
    });
}

// Setup Character Counter
function setupCharacterCounter() {
    const editor = document.getElementById('editor');

    updateCharCount(); // Initial count

    editor.addEventListener('input', function() {
        updateCharCount();
    });
}



// Create New Template Function
function createNewTemplate() {
    console.log("Creating new template");

    // Clear selection from all items
    document.querySelectorAll('.template-item').forEach(i => i.classList.remove('selected'));

    // Clear editor and title
    document.getElementById('editor').innerHTML = '<p>Enter your template content here...</p>';
    document.getElementById('template-title-input').value = 'New Template';
    document.getElementById('template-to-delete').textContent = 'New Template';

    // Focus on title input
    document.getElementById('template-title-input').focus();

    // Update character count
    updateCharCount();
}

// Setup Save Template Functionality
function setupSaveTemplate() {
    document.getElementById('save-template-btn').addEventListener('click', function() {
        console.log("Save button clicked");

        const title = document.getElementById('template-title-input').value.trim();
        const description = document.getElementById('editor').innerHTML;

        // Validate title
        if (!title) {
            showToast('Please enter a template title', 'error');
            return;
        }

        // Validate content
        if (!description || description === '<p>Enter your template content here...</p>') {
            showToast('Please enter some content for your template', 'error');
            return;
        }

        // Check character limit
        if (document.getElementById('editor').textContent.length > 10000) {
            showToast('Template content exceeds the character limit of 10000', 'error');
            return;
        }

        // Send the data to the server to save the template
        saveTemplateToDatabase(title, description);
    });
}

// Save Template To Database
function saveTemplateToDatabase(title, description) {
    console.log("Saving template:", title);

    const csrftoken = getCookie('csrftoken');

    fetch('/api/save-template/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrftoken
        },
        body: JSON.stringify({
            title: title,
            description: description
        }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Template saved successfully', 'success');

            // Reload templates from database to show the updated list
            loadTemplatesFromDatabase();
        } else {
            showToast('Failed to save template', 'error');
        }
    })
    .catch(error => {
        console.error('Error saving template:', error);
        showToast('Failed to save template to the server', 'error');
    });
}

// Setup Delete Template Functionality
function setupDeleteTemplate() {
    const deleteBtn = document.getElementById('delete-template-btn');
    const deleteModal = document.getElementById('delete-template-modal');
    const cancelDeleteBtn = document.getElementById('cancel-delete');
    const confirmDeleteBtn = document.getElementById('confirm-delete');
    const closeModalBtn = document.querySelector('#delete-template-modal .close-modal');

    // Show delete modal
    deleteBtn.addEventListener('click', function() {
        console.log("Delete button clicked");

        // Check if a template is selected
        const selectedItem = document.querySelector('.template-item.selected');
        if (!selectedItem) {
            showToast('Please select a template to delete', 'error');
            return;
        }

        // Set template name in confirmation message
        const templateTitle = selectedItem.querySelector('.template-title').textContent;
        document.getElementById('template-to-delete').textContent = templateTitle;

        // Show modal
        deleteModal.style.display = 'block';
    });

    // Close modal
    function closeModal() {
        deleteModal.style.display = 'none';
    }

    cancelDeleteBtn.addEventListener('click', closeModal);
    closeModalBtn.addEventListener('click', closeModal);

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === deleteModal) {
            closeModal();
        }
    });

    // Confirm delete
    confirmDeleteBtn.addEventListener('click', function() {
        const selectedItem = document.querySelector('.template-item.selected');

        if (selectedItem) {
            const templateTitle = selectedItem.querySelector('.template-title').textContent;

            // Send delete request to the server
            deleteTemplateFromDatabase(templateTitle);

            closeModal();
        }
    });
}

// Delete Template From Database
function deleteTemplateFromDatabase(title) {
    console.log("Deleting template:", title);

    const csrftoken = getCookie('csrftoken');

    fetch('/api/delete-template/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrftoken
        },
        body: JSON.stringify({
            title: title
        }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Template deleted successfully', 'success');

            // Reload templates from database to show the updated list
            loadTemplatesFromDatabase();
        } else {
            showToast('Failed to delete template', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting template:', error);
        showToast('Failed to delete template from the server', 'error');
    });
}

// Initialize Toast Notifications
function initToast() {
    const toast = document.getElementById('toast');
    const toastClose = document.querySelector('.toast-close');

    // Close toast when clicking the X
    toastClose.addEventListener('click', function() {
        toast.classList.remove('show');
    });
}

// Initialize Search Functionality
function initSearch() {
    const searchInput = document.getElementById('search-templates');

    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        const templates = document.querySelectorAll('.template-item');

        templates.forEach(template => {
            const title = template.querySelector('.template-title').textContent.toLowerCase();

            if (title.includes(query)) {
                template.style.display = '';
            } else {
                template.style.display = 'none';
            }
        });
    });
}

</script>
{% endblock %}