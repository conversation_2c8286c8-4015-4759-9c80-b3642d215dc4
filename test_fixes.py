#!/usr/bin/env python
"""
Test script to verify the implemented fixes work correctly.
This script tests the three main issues that were fixed:
1. Job description validation (40 character minimum)
2. HTML generation for career pages
3. Workloupe profile validation
"""

import os
import django
import json
from bs4 import BeautifulSoup
import re

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'canvider.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from feed.models import Employee, Employer
from feed.views import generate_widget_html, generate_full_page_html

def test_job_description_validation():
    """Test job description validation with 40 character minimum"""
    print("=" * 60)
    print("TESTING JOB DESCRIPTION VALIDATION")
    print("=" * 60)
    
    # Test cases for job description validation
    test_cases = [
        {
            'description': '<p>Short</p>',
            'expected_valid': False,
            'name': 'Very short description'
        },
        {
            'description': '<p>This is a test job description that is exactly forty characters long.</p>',
            'expected_valid': True,
            'name': 'Exactly 40 characters'
        },
        {
            'description': '<p>This is a <strong>longer</strong> job description with HTML tags that should pass validation easily.</p>',
            'expected_valid': True,
            'name': 'Long description with HTML'
        },
        {
            'description': '<p>   </p><div>   </div>',
            'expected_valid': False,
            'name': 'Only whitespace and HTML tags'
        },
        {
            'description': '',
            'expected_valid': False,
            'name': 'Empty description'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Input: {test_case['description']}")
        
        # Extract text content from HTML and count actual characters
        try:
            soup = BeautifulSoup(test_case['description'], 'html.parser')
            text_content = soup.get_text()
            clean_text = re.sub(r'\s+', ' ', text_content).strip()
            text_length = len(clean_text)
            
            is_valid = text_length >= 40
            
            print(f"Extracted text: '{clean_text}'")
            print(f"Text length: {text_length} characters")
            print(f"Validation result: {'PASS' if is_valid else 'FAIL'}")
            print(f"Expected: {'PASS' if test_case['expected_valid'] else 'FAIL'}")
            
            if is_valid == test_case['expected_valid']:
                print("✅ Test PASSED")
            else:
                print("❌ Test FAILED")
                
        except Exception as e:
            print(f"❌ Error during validation: {e}")
    
    print("\n" + "=" * 60)

def test_html_generation():
    """Test HTML generation for career pages"""
    print("TESTING HTML GENERATION")
    print("=" * 60)
    
    # Test widget HTML generation
    print("\n1. Testing Widget HTML Generation:")
    try:
        widget_html = generate_widget_html(
            company_name="Test Company",
            tagline="Join our amazing team",
            logo_url="https://example.com/logo.png",
            banner_url="https://example.com/banner.png",
            rss_feed_url="https://workloupe.com/company/test/jobs.rss",
            second_title="Open Positions",
            second_tagline="Explore opportunities",
            primary_color="#007bff",
            secondary_color="#6c757d",
            max_jobs=5,
            show_salary=True,
            show_location=True,
            show_date=True
        )
        
        if widget_html and len(widget_html) > 100:
            print("✅ Widget HTML generated successfully")
            print(f"   Length: {len(widget_html)} characters")
            
            # Check for XSS protection
            if '<script>' in widget_html and 'alert(' not in widget_html:
                print("✅ HTML contains script tags but no malicious content")
            else:
                print("✅ HTML appears safe from XSS")
                
        else:
            print("❌ Widget HTML generation failed or too short")
            
    except Exception as e:
        print(f"❌ Widget HTML generation error: {e}")
    
    # Test full page HTML generation
    print("\n2. Testing Full Page HTML Generation:")
    try:
        full_html = generate_full_page_html(
            company_name="Test Company",
            tagline="Join our amazing team",
            logo_url="https://example.com/logo.png",
            banner_url="https://example.com/banner.png",
            rss_feed_url="https://workloupe.com/company/test/jobs.rss",
            second_title="Open Positions",
            second_tagline="Explore opportunities",
            primary_color="#007bff",
            secondary_color="#6c757d",
            enable_search=True,
            is_full_page=True,
            is_widget=False
        )
        
        if full_html and len(full_html) > 500:
            print("✅ Full page HTML generated successfully")
            print(f"   Length: {len(full_html)} characters")
            
            # Check for proper HTML structure
            if '<!DOCTYPE html>' in full_html and '</html>' in full_html:
                print("✅ HTML has proper document structure")
            else:
                print("❌ HTML missing proper document structure")
                
        else:
            print("❌ Full page HTML generation failed or too short")
            
    except Exception as e:
        print(f"❌ Full page HTML generation error: {e}")
    
    print("\n" + "=" * 60)

def test_workloupe_profile_validation():
    """Test Workloupe profile field validation"""
    print("TESTING WORKLOUPE PROFILE VALIDATION")
    print("=" * 60)
    
    # Test required field validation
    test_cases = [
        {
            'data': {
                'employer_name': '',
                'employer_email': '<EMAIL>',
                'employer_website': 'https://company.com',
                'employer_description': 'Test description'
            },
            'expected_error': 'Company name is required',
            'name': 'Missing company name'
        },
        {
            'data': {
                'employer_name': 'Test Company',
                'employer_email': '',
                'employer_website': 'https://company.com',
                'employer_description': 'Test description'
            },
            'expected_error': 'Company email is required',
            'name': 'Missing company email'
        },
        {
            'data': {
                'employer_name': 'Test Company',
                'employer_email': '<EMAIL>',
                'employer_website': '',
                'employer_description': 'Test description'
            },
            'expected_error': 'Company website is required',
            'name': 'Missing company website'
        },
        {
            'data': {
                'employer_name': 'Test Company',
                'employer_email': '<EMAIL>',
                'employer_website': 'https://company.com',
                'employer_description': ''
            },
            'expected_error': 'Company description is required',
            'name': 'Missing company description'
        },
        {
            'data': {
                'employer_name': 'Test Company',
                'employer_email': '<EMAIL>',
                'employer_website': 'https://company.com',
                'employer_description': 'Valid description'
            },
            'expected_error': None,
            'name': 'All required fields present'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        
        # Simulate validation logic from the view
        data = test_case['data']
        employer_name = data.get('employer_name', '').strip()
        employer_email = data.get('employer_email', '').strip()
        employer_website = data.get('employer_website', '').strip()
        employer_description = data.get('employer_description', '').strip()
        
        error = None
        if not employer_name:
            error = 'Company name is required'
        elif not employer_email:
            error = 'Company email is required'
        elif not employer_website:
            error = 'Company website is required'
        elif not employer_description:
            error = 'Company description is required'
        
        print(f"Validation result: {error or 'VALID'}")
        print(f"Expected: {test_case['expected_error'] or 'VALID'}")
        
        if error == test_case['expected_error']:
            print("✅ Test PASSED")
        else:
            print("❌ Test FAILED")
    
    print("\n" + "=" * 60)

def main():
    """Run all tests"""
    print("RUNNING CANVIDER FIXES VALIDATION TESTS")
    print("=" * 60)
    print("Testing the three main fixes implemented:")
    print("1. Job description validation (40 character minimum)")
    print("2. HTML generation for career pages")
    print("3. Workloupe profile field validation")
    print("=" * 60)
    
    try:
        test_job_description_validation()
        test_html_generation()
        test_workloupe_profile_validation()
        
        print("ALL TESTS COMPLETED")
        print("=" * 60)
        print("✅ If all tests show PASSED, the fixes are working correctly!")
        print("❌ If any tests show FAILED, there may be issues to investigate.")
        
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
